(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{1439:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>F});var r={};t.r(r);var a=t(5155),n=t(2115);let l=t(3464).A.create({baseURL:"/api/backend",timeout:1e4}),c={getCurrentData:async()=>(await l.get("/ethereum/current")).data,async getHistoricalData(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:30;return(await l.get("/ethereum/historical?days=".concat(e))).data},async getAgentAnalysis(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"comprehensive";return(await l.post("/analysis/run",{mode:e})).data},getAlerts:async()=>(await l.get("/alerts")).data,submitQuery:async e=>(await l.post("/analysis/query",{query:e})).data.response,async healthCheck(){try{let e=await l.get("/health");return 200===e.status}catch(e){return!1}}};var i=t(2596),d=t(9688);function o(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,d.QP)((0,i.$)(s))}function m(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e)}let x=n.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:o("rounded-lg border bg-card text-card-foreground shadow-sm",t),...r})});x.displayName="Card";let h=n.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:o("flex flex-col space-y-1.5 p-6",t),...r})});h.displayName="CardHeader";let u=n.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("h3",{ref:s,className:o("text-2xl font-semibold leading-none tracking-tight",t),...r})});u.displayName="CardTitle",n.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("p",{ref:s,className:o("text-sm text-muted-foreground",t),...r})}).displayName="CardDescription";let g=n.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:o("p-6 pt-0",t),...r})});function f(e){let{className:s,variant:t="default",...r}=e;return(0,a.jsx)("div",{className:o("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{"border-transparent bg-primary text-primary-foreground hover:bg-primary/80":"default"===t,"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80":"secondary"===t,"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80":"destructive"===t,"text-foreground":"outline"===t},s),...r})}g.displayName="CardContent",n.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:o("flex items-center p-6 pt-0",t),...r})}).displayName="CardFooter";let j=n.forwardRef((e,s)=>{let{className:t,variant:r="default",size:n="default",...l}=e;return(0,a.jsx)("button",{className:o("inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{"bg-primary text-primary-foreground hover:bg-primary/90":"default"===r,"bg-destructive text-destructive-foreground hover:bg-destructive/90":"destructive"===r,"border border-input bg-background hover:bg-accent hover:text-accent-foreground":"outline"===r,"bg-secondary text-secondary-foreground hover:bg-secondary/80":"secondary"===r,"hover:bg-accent hover:text-accent-foreground":"ghost"===r,"text-primary underline-offset-4 hover:underline":"link"===r},{"h-10 px-4 py-2":"default"===n,"h-9 rounded-md px-3":"sm"===n,"h-11 rounded-md px-8":"lg"===n,"h-10 w-10":"icon"===n},t),ref:s,...l})});j.displayName="Button";var p=t(9727),b=t(7112),y=t(4565),N=t(3904),v=t(1243),w=t(9376),k=t(3109),C=t(9397);let A=()=>{let{currentData:e,historicalData:s,analysis:t,alerts:l,loading:i,error:d,connectionStatus:o,refreshAll:A,fetchAnalysis:F}=(()=>{let[e,s]=(0,n.useState)(null),[t,r]=(0,n.useState)([]),[a,l]=(0,n.useState)([]),[i,d]=(0,n.useState)([]),[o,m]=(0,n.useState)(!0),[x,h]=(0,n.useState)(null),[u,g]=(0,n.useState)("checking"),f=(0,n.useCallback)(async()=>{try{let e=await c.healthCheck();g(e?"connected":"disconnected")}catch(e){g("disconnected")}},[]),j=(0,n.useCallback)(async()=>{try{let e=await c.getCurrentData();s(e),h(null)}catch(e){console.error("Failed to fetch current data:",e),h("Failed to fetch current Ethereum data")}},[]),p=(0,n.useCallback)(async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:30;try{let s=await c.getHistoricalData(e);r(s)}catch(e){console.error("Failed to fetch historical data:",e)}},[]),b=(0,n.useCallback)(async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"comprehensive";try{m(!0);let s=await c.getAgentAnalysis(e);l(s)}catch(e){console.error("Failed to fetch analysis:",e),h("Failed to fetch AI analysis")}finally{m(!1)}},[]),y=(0,n.useCallback)(async()=>{try{let e=await c.getAlerts();d(e)}catch(e){console.error("Failed to fetch alerts:",e)}},[]),N=(0,n.useCallback)(async e=>{try{return await c.submitQuery(e)}catch(e){throw console.error("Failed to submit query:",e),Error("Failed to submit query to AI agents")}},[]),v=(0,n.useCallback)(async()=>{m(!0);try{await Promise.all([j(),p(),y()])}catch(e){console.error("Failed to refresh data:",e)}finally{m(!1)}},[j,p,y]);return(0,n.useEffect)(()=>{(async()=>{await f(),"connected"===u&&await v()})()},[u,f,v]),(0,n.useEffect)(()=>{if("connected"!==u)return;let e=[setInterval(j,3e4),setInterval(y,6e4),setInterval(f,12e4)];return()=>{e.forEach(clearInterval)}},[u,j,y,f]),{currentData:e,historicalData:t,analysis:a,alerts:i,loading:o,error:x,connectionStatus:u,refreshAll:v,fetchAnalysis:b,fetchHistoricalData:p,submitQuery:N,checkConnection:f}})(),[_,E]=(0,n.useState)(!1),I=async e=>{E(!0);try{await F(e)}finally{E(!1)}},S=e=>{let{change:s}=e;return(0,a.jsxs)("div",{className:"flex items-center gap-1 ".concat(s>=0?"text-green-500":"text-red-500"),children:[s>=0?(0,a.jsx)(b.A,{className:"h-4 w-4"}):(0,a.jsx)(y.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"font-medium",children:function(e){return"".concat(e>=0?"+":"").concat(e.toFixed(2),"%")}(s)})]})};return d&&"disconnected"===o?(0,a.jsx)("div",{className:"container mx-auto p-6",children:(0,a.jsx)(x,{className:"border-red-200 bg-red-50",children:(0,a.jsxs)(g,{className:"flex items-center justify-between p-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(v.A,{className:"h-6 w-6 text-red-500"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-red-700",children:"Backend Connection Error"}),(0,a.jsx)("p",{className:"text-sm text-red-600",children:"Unable to connect to the Ethereum tracking backend. Please ensure the Python backend is running on localhost:8000"})]})]}),(0,a.jsxs)(j,{onClick:A,variant:"outline",size:"sm",children:[(0,a.jsx)(N.A,{className:"h-4 w-4 mr-2"}),"Retry"]})]})})}):(0,a.jsxs)("div",{className:"container mx-auto p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Ethereum Tracker"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"AI-powered real-time analysis dashboard"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)(()=>(0,a.jsx)("div",{className:"flex items-center gap-2",children:"connected"===o?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p.A,{className:"h-4 w-4 text-green-500"}),(0,a.jsx)("span",{className:"text-sm text-green-500",children:"Connected"})]}):"disconnected"===o?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(r.WifiOffIcon,{className:"h-4 w-4 text-red-500"}),(0,a.jsx)("span",{className:"text-sm text-red-500",children:"Disconnected"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(N.A,{className:"h-4 w-4 animate-spin text-yellow-500"}),(0,a.jsx)("span",{className:"text-sm text-yellow-500",children:"Checking..."})]})}),{}),(0,a.jsxs)(j,{onClick:A,disabled:i,variant:"outline",size:"sm",children:[(0,a.jsx)(N.A,{className:"h-4 w-4 mr-2 ".concat(i?"animate-spin":"")}),"Refresh"]})]})]}),e&&(0,a.jsxs)(x,{className:"mb-6",children:[(0,a.jsx)(h,{children:(0,a.jsxs)(u,{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{children:"Ethereum (ETH)"}),(0,a.jsx)(f,{variant:"secondary",children:"Live"})]})}),(0,a.jsx)(g,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-1",children:"Current Price"}),(0,a.jsx)("p",{className:"text-3xl font-bold",children:m(e.price)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-1",children:"24h Change"}),(0,a.jsx)(S,{change:e.change_24h})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-1",children:"7d Change"}),(0,a.jsx)(S,{change:e.change_7d})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-1",children:"24h Volume"}),(0,a.jsx)("p",{className:"text-xl font-semibold",children:m(e.volume_24h)})]})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6",children:[(0,a.jsxs)(x,{children:[(0,a.jsx)(h,{children:(0,a.jsxs)(u,{className:"flex items-center gap-2",children:[(0,a.jsx)(w.A,{className:"h-5 w-5"}),"AI Agent Analysis"]})}),(0,a.jsx)(g,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(j,{onClick:()=>I("comprehensive"),disabled:_,size:"sm",children:[_?(0,a.jsx)(N.A,{className:"h-4 w-4 mr-2 animate-spin"}):(0,a.jsx)(k.A,{className:"h-4 w-4 mr-2"}),"Full Analysis"]}),(0,a.jsx)(j,{onClick:()=>I("quick"),disabled:_,variant:"outline",size:"sm",children:"Quick Check"})]}),t.length>0?(0,a.jsxs)("div",{className:"space-y-3",children:[t.slice(0,3).map((e,s)=>(0,a.jsxs)("div",{className:"border-l-2 border-blue-500 pl-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,a.jsx)("p",{className:"font-medium text-sm",children:e.agent}),(0,a.jsxs)(f,{variant:"outline",children:[Math.round(100*e.confidence),"% confidence"]})]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground line-clamp-2",children:e.analysis})]},s)),t.length>3&&(0,a.jsxs)("p",{className:"text-sm text-muted-foreground text-center",children:["+",t.length-3," more analyses available"]})]}):(0,a.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,a.jsx)(w.A,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),(0,a.jsx)("p",{className:"text-sm",children:'Click "Full Analysis" to get AI insights'})]})]})})]}),(0,a.jsxs)(x,{children:[(0,a.jsx)(h,{children:(0,a.jsxs)(u,{className:"flex items-center gap-2",children:[(0,a.jsx)(v.A,{className:"h-5 w-5"}),"Active Alerts"]})}),(0,a.jsx)(g,{children:l.length>0?(0,a.jsx)("div",{className:"space-y-3",children:l.slice(0,5).map((e,s)=>(0,a.jsx)("div",{className:"p-3 rounded-lg border ".concat("critical"===e.severity?"bg-red-50 border-red-200":"high"===e.severity?"bg-orange-50 border-orange-200":"medium"===e.severity?"bg-yellow-50 border-yellow-200":"bg-blue-50 border-blue-200"),children:(0,a.jsx)("div",{className:"flex items-start justify-between",children:(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)(f,{variant:"critical"===e.severity?"destructive":"secondary",className:"mb-2",children:e.type.toUpperCase()}),(0,a.jsx)("p",{className:"text-sm",children:e.message})]})})},s))}):(0,a.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,a.jsx)(C.A,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),(0,a.jsx)("p",{className:"text-sm",children:"No active alerts"})]})})]})]}),e&&(0,a.jsxs)(x,{children:[(0,a.jsx)(h,{children:(0,a.jsx)(u,{children:"Market Statistics"})}),(0,a.jsx)(g,{children:(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center p-4 border rounded-lg",children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Market Cap"}),(0,a.jsx)("p",{className:"text-lg font-semibold",children:m(e.market_cap)})]}),(0,a.jsxs)("div",{className:"text-center p-4 border rounded-lg",children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"24h Volume"}),(0,a.jsx)("p",{className:"text-lg font-semibold",children:m(e.volume_24h)})]}),e.technical_indicators&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"text-center p-4 border rounded-lg",children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"RSI"}),(0,a.jsx)("p",{className:"text-lg font-semibold",children:e.technical_indicators.rsi.toFixed(1)})]}),(0,a.jsxs)("div",{className:"text-center p-4 border rounded-lg",children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"MACD"}),(0,a.jsx)("p",{className:"text-lg font-semibold",children:e.technical_indicators.macd.toFixed(4)})]})]})]})})]})]})};function F(){return(0,a.jsx)("main",{className:"min-h-screen bg-background",children:(0,a.jsx)(A,{})})}},2287:(e,s,t)=>{Promise.resolve().then(t.bind(t,1439))}},e=>{e.O(0,[549,441,964,358],()=>e(e.s=2287)),_N_E=e.O()}]);