(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{3015:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,9324,23)),Promise.resolve().then(t.t.bind(t,5299,23))},5299:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c"}},9324:()=>{throw Error("Module build failed (from ./node_modules/next/dist/compiled/mini-css-extract-plugin/loader.js):\nHookWebpackError: Module build failed (from ./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js):\nError: It looks like you're trying to use `tailwindcss` directly as a PostCSS plugin. The PostCSS plugin has moved to a separate package, so to continue using Tailwind CSS with PostCSS you'll need to install `@tailwindcss/postcss` and update your PostCSS configuration.\n    at We (/Users/<USER>/Projects/eth-track/frontend/node_modules/tailwindcss/dist/lib.js:35:2121)\n    at LazyResult.runOnRoot (/Users/<USER>/Projects/eth-track/frontend/node_modules/next/node_modules/postcss/lib/lazy-result.js:329:16)\n    at LazyResult.runAsync (/Users/<USER>/Projects/eth-track/frontend/node_modules/next/node_modules/postcss/lib/lazy-result.js:258:26)\n    at LazyResult.async (/Users/<USER>/Projects/eth-track/frontend/node_modules/next/node_modules/postcss/lib/lazy-result.js:160:30)\n    at LazyResult.then (/Users/<USER>/Projects/eth-track/frontend/node_modules/next/node_modules/postcss/lib/lazy-result.js:404:17)\n    at tryRunOrWebpackError (/Users/<USER>/Projects/eth-track/frontend/node_modules/next/dist/compiled/webpack/bundle5.js:29:316119)\n    at __webpack_require_module__ (/Users/<USER>/Projects/eth-track/frontend/node_modules/next/dist/compiled/webpack/bundle5.js:29:131532)\n    at __nested_webpack_require_161494__ (/Users/<USER>/Projects/eth-track/frontend/node_modules/next/dist/compiled/webpack/bundle5.js:29:130967)\n    at /Users/<USER>/Projects/eth-track/frontend/node_modules/next/dist/compiled/webpack/bundle5.js:29:131824\n    at symbolIterator (/Users/<USER>/Projects/eth-track/frontend/node_modules/next/dist/compiled/neo-async/async.js:1:14444)\n    at done (/Users/<USER>/Projects/eth-track/frontend/node_modules/next/dist/compiled/neo-async/async.js:1:14824)\n    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Projects/eth-track/frontend/node_modules/next/dist/compiled/webpack/bundle5.js:14:9224), <anonymous>:15:1)\n    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Projects/eth-track/frontend/node_modules/next/dist/compiled/webpack/bundle5.js:14:6378)\n    at /Users/<USER>/Projects/eth-track/frontend/node_modules/next/dist/compiled/webpack/bundle5.js:29:130687\n    at symbolIterator (/Users/<USER>/Projects/eth-track/frontend/node_modules/next/dist/compiled/neo-async/async.js:1:14402)\n-- inner error --\nError: Module build failed (from ./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js):\nError: It looks like you're trying to use `tailwindcss` directly as a PostCSS plugin. The PostCSS plugin has moved to a separate package, so to continue using Tailwind CSS with PostCSS you'll need to install `@tailwindcss/postcss` and update your PostCSS configuration.\n    at We (/Users/<USER>/Projects/eth-track/frontend/node_modules/tailwindcss/dist/lib.js:35:2121)\n    at LazyResult.runOnRoot (/Users/<USER>/Projects/eth-track/frontend/node_modules/next/node_modules/postcss/lib/lazy-result.js:329:16)\n    at LazyResult.runAsync (/Users/<USER>/Projects/eth-track/frontend/node_modules/next/node_modules/postcss/lib/lazy-result.js:258:26)\n    at LazyResult.async (/Users/<USER>/Projects/eth-track/frontend/node_modules/next/node_modules/postcss/lib/lazy-result.js:160:30)\n    at LazyResult.then (/Users/<USER>/Projects/eth-track/frontend/node_modules/next/node_modules/postcss/lib/lazy-result.js:404:17)\n    at Object.<anonymous> (/Users/<USER>/Projects/eth-track/frontend/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!/Users/<USER>/Projects/eth-track/frontend/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!/Users/<USER>/Projects/eth-track/frontend/app/globals.css:1:7)\n    at /Users/<USER>/Projects/eth-track/frontend/node_modules/next/dist/compiled/webpack/bundle5.js:29:962717\n    at Hook.eval [as call] (eval at create (/Users/<USER>/Projects/eth-track/frontend/node_modules/next/dist/compiled/webpack/bundle5.js:14:9002), <anonymous>:7:1)\n    at Hook.CALL_DELEGATE [as _call] (/Users/<USER>/Projects/eth-track/frontend/node_modules/next/dist/compiled/webpack/bundle5.js:14:6272)\n    at /Users/<USER>/Projects/eth-track/frontend/node_modules/next/dist/compiled/webpack/bundle5.js:29:131565\n    at tryRunOrWebpackError (/Users/<USER>/Projects/eth-track/frontend/node_modules/next/dist/compiled/webpack/bundle5.js:29:316073)\n    at __webpack_require_module__ (/Users/<USER>/Projects/eth-track/frontend/node_modules/next/dist/compiled/webpack/bundle5.js:29:131532)\n    at __nested_webpack_require_161494__ (/Users/<USER>/Projects/eth-track/frontend/node_modules/next/dist/compiled/webpack/bundle5.js:29:130967)\n    at /Users/<USER>/Projects/eth-track/frontend/node_modules/next/dist/compiled/webpack/bundle5.js:29:131824\n    at symbolIterator (/Users/<USER>/Projects/eth-track/frontend/node_modules/next/dist/compiled/neo-async/async.js:1:14444)\n\nGenerated code for /Users/<USER>/Projects/eth-track/frontend/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!/Users/<USER>/Projects/eth-track/frontend/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!/Users/<USER>/Projects/eth-track/frontend/app/globals.css\n1 | throw new Error(\"Module build failed (from ./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js):\\nError: It looks like you're trying to use `tailwindcss` directly as a PostCSS plugin. The PostCSS plugin has moved to a separate package, so to continue using Tailwind CSS with PostCSS you'll need to install `@tailwindcss/postcss` and update your PostCSS configuration.\\n    at We (/Users/<USER>/Projects/eth-track/frontend/node_modules/tailwindcss/dist/lib.js:35:2121)\\n    at LazyResult.runOnRoot (/Users/<USER>/Projects/eth-track/frontend/node_modules/next/node_modules/postcss/lib/lazy-result.js:329:16)\\n    at LazyResult.runAsync (/Users/<USER>/Projects/eth-track/frontend/node_modules/next/node_modules/postcss/lib/lazy-result.js:258:26)\\n    at LazyResult.async (/Users/<USER>/Projects/eth-track/frontend/node_modules/next/node_modules/postcss/lib/lazy-result.js:160:30)\\n    at LazyResult.then (/Users/<USER>/Projects/eth-track/frontend/node_modules/next/node_modules/postcss/lib/lazy-result.js:404:17)\");")}},e=>{e.O(0,[854,441,964,358],()=>e(e.s=3015)),_N_E=e.O()}]);